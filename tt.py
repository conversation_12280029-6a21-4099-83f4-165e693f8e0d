import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from scipy.signal import butter, filtfilt
import matplotlib.patches as patches
# from scipy.fft import fft, ifft, fftfreq  # 不再需要，已改为阶跃函数实现

# --- 1. 参数设置 - 更真实的数据 ---
SAMPLING_RATE = 500   # 采样率
DURATION = 1.0        # 时间窗口
N_SAMPLES = int(DURATION * SAMPLING_RATE)

# 基础频率参数
F1_BASE = 12  # 基础频率
F2_BASE = 18  # 次频率

# 噪声参数
NOISE_AMPLITUDE = 0.20
DRIFT_AMPLITUDE = 0.12

# 攻击参数 - 修改为低频攻击
ATTACK_START_TIME = 0.35
ATTACK_END_TIME = 0.65
ATTACK_FREQUENCY = 8  # 改为低频攻击 (8Hz)

# --- 2. 生成更真实的信号 ---
t = np.linspace(0, DURATION, N_SAMPLES, endpoint=False)

# (1) 创建真实的基础信号 - 包含频率漂移和幅度调制
# 频率漂移
freq_drift = F1_BASE + 2 * np.sin(2 * np.pi * 0.8 * t)
freq_drift2 = F2_BASE + 1.5 * np.sin(2 * np.pi * 1.2 * t)

# 幅度调制
amplitude_mod = 1.0 + 0.2 * np.sin(2 * np.pi * 3 * t)
amplitude_mod2 = 0.5 + 0.1 * np.sin(2 * np.pi * 2.5 * t)

# 基础信号
base_signal = (amplitude_mod * np.sin(2 * np.pi * freq_drift * t) + 
               amplitude_mod2 * np.sin(2 * np.pi * freq_drift2 * t))

# 添加基线漂移
baseline_drift = DRIFT_AMPLITUDE * np.sin(2 * np.pi * 0.3 * t) + 0.05 * t

# 添加谐波失真
harmonic_distortion = 0.1 * np.sin(2 * np.pi * freq_drift * 3 * t)

normal_signal_realistic = base_signal + baseline_drift + harmonic_distortion

# (2) 创建真实的噪声 - 非平稳噪声
# 高斯白噪声 - 减少噪声强度
white_noise = (NOISE_AMPLITUDE * 0.3) * np.random.randn(N_SAMPLES)

# 有色噪声 (1/f噪声) - 减少有色噪声
colored_noise = np.zeros(N_SAMPLES)
for i in range(1, N_SAMPLES//4):  # 减少频率成分
    phase = np.random.uniform(0, 2*np.pi)
    colored_noise += (1/i) * np.sin(2 * np.pi * i * t / DURATION + phase)
colored_noise *= NOISE_AMPLITUDE * 0.1  # 大幅减少有色噪声

# 脉冲噪声 - 减少脉冲数量
impulse_noise = np.zeros(N_SAMPLES)
impulse_positions = np.random.choice(N_SAMPLES, size=2, replace=False)  # 减少脉冲数量
impulse_noise[impulse_positions] = np.random.uniform(-0.2, 0.2, len(impulse_positions))  # 减少脉冲幅度

# (3) 原始信号 (a图) - 只有轻微噪声，没有攻击
original_signal = normal_signal_realistic + white_noise + colored_noise + impulse_noise

# (4) 纯净信号 (b图) - 通过滤波获得去噪后的信号
def design_clean_filter():
    # 设计带通滤波器保留正常信号频率
    low_cutoff = 8  # Hz
    high_cutoff = 30  # Hz
    nyquist = SAMPLING_RATE / 2
    low = low_cutoff / nyquist
    high = high_cutoff / nyquist
    
    b, a = butter(4, [low, high], btype='band')
    return b, a

b, a = design_clean_filter()
clean_signal = filtfilt(b, a, original_signal)

# 使用更温和的平滑，避免过度去噪
smooth_window = np.hanning(5)  # 进一步减少窗口大小，从7减少到5
smooth_window = smooth_window / np.sum(smooth_window)
clean_signal = np.convolve(clean_signal, smooth_window, 'same')

# 保留更多原始信号特征，减少去噪强度
# 将去噪后的信号与原始信号进行加权混合，保持更多原始特征
clean_signal = 0.8 * clean_signal + 0.2 * original_signal  # 增加原始信号比例

# 特别处理0.6-1.0秒区间，几乎保持原始信号不变
problem_start_idx = int(0.6 * SAMPLING_RATE)
problem_end_idx = int(1.0 * SAMPLING_RATE)
if problem_end_idx > N_SAMPLES:
    problem_end_idx = N_SAMPLES

# 在问题区间几乎保持原始信号，只做极轻微的平滑
clean_signal[problem_start_idx:problem_end_idx] = (
    0.50 * original_signal[problem_start_idx:problem_end_idx] + 
    0.50 * clean_signal[problem_start_idx:problem_end_idx]
)

# 保留合理的高频残差 - 模拟传感器微振动，但减少强度
sensor_micro_vibration = 0.02 * np.random.randn(N_SAMPLES)  # 进一步减少微振动
# 添加低频微振动成分，但减少幅度
micro_vibration_low = 0.015 * np.sin(2 * np.pi * 25 * t)  # 25Hz微振动，减少幅度
micro_vibration_high = 0.008 * np.sin(2 * np.pi * 45 * t)  # 45Hz微振动，减少幅度
clean_signal += sensor_micro_vibration + micro_vibration_low + micro_vibration_high

# (5) 基于低频攻击的对抗性信号生成逻辑
def generate_adversarial_low_frequency_signal(original_signal, clean_signal, attack_strength=1.2):
    """
    生成真正的低频攻击信号：
    1. 使用低频正弦波作为攻击信号（缓慢变化，平滑）
    2. 在特定时间段内增强低频攻击的幅度
    3. 添加适度的随机噪声模拟真实攻击环境
    """
    # 从干净信号开始构建对抗性信号
    adversarial_signal = clean_signal.copy()

    # 定义低频攻击参数
    attack_frequency = 3.0  # 3Hz低频攻击
    attack_start_time = 0.3  # 攻击开始时间
    attack_end_time = 0.8    # 攻击结束时间

    # 重点攻击区域（红色标识区域）
    highlight_start_time = 0.575
    highlight_end_time = 0.625

    # 计算时间索引
    attack_start_idx = int(attack_start_time * SAMPLING_RATE)
    attack_end_idx = int(attack_end_time * SAMPLING_RATE)
    highlight_start_idx = int(highlight_start_time * SAMPLING_RATE)
    highlight_end_idx = int(highlight_end_time * SAMPLING_RATE)

    # 确保索引在有效范围内
    attack_start_idx = max(0, attack_start_idx)
    attack_end_idx = min(N_SAMPLES, attack_end_idx)
    highlight_start_idx = max(0, highlight_start_idx)
    highlight_end_idx = min(N_SAMPLES, highlight_end_idx)

    # 生成低频攻击信号（平滑的正弦波）
    low_freq_attack = np.zeros(N_SAMPLES)

    # 在攻击时间段内生成低频正弦波
    if attack_start_idx < attack_end_idx:
        attack_time = t[attack_start_idx:attack_end_idx]
        # 使用包络函数使攻击信号平滑开始和结束
        envelope = np.sin(np.pi * (attack_time - attack_start_time) / (attack_end_time - attack_start_time))
        low_freq_component = np.sin(2 * np.pi * attack_frequency * attack_time)
        low_freq_attack[attack_start_idx:attack_end_idx] = envelope * low_freq_component

    # 在重点区域增强攻击强度
    base_amplitude = 0.8 * attack_strength
    highlight_amplitude = 1.5 * attack_strength

    # 应用低频攻击
    adversarial_signal += base_amplitude * low_freq_attack

    # 在红色标识区域增强攻击
    if highlight_start_idx < highlight_end_idx:
        highlight_enhancement = (highlight_amplitude - base_amplitude) * low_freq_attack[highlight_start_idx:highlight_end_idx]
        adversarial_signal[highlight_start_idx:highlight_end_idx] += highlight_enhancement

    # 添加轻微的随机噪声模拟真实环境
    # 只在部分区域添加噪声（60%的区域）
    noise_mask = np.random.choice([True, False], size=N_SAMPLES, p=[0.6, 0.4])
    additional_noise = np.random.randn(N_SAMPLES) * 0.1  # 很小的噪声
    adversarial_signal[noise_mask] += additional_noise[noise_mask]

    # 为整个信号添加一点点额外的全局噪声
    global_noise = np.random.randn(N_SAMPLES) * 0.03  # 非常轻微的全局噪声
    adversarial_signal += global_noise

    # 计算对抗性噪声（用于分析）
    adversarial_noise = adversarial_signal - clean_signal
    residual_noise = original_signal - clean_signal

    return adversarial_signal, adversarial_noise, residual_noise

# 生成基于低频攻击的对抗性信号
adversarial_signal, adversarial_noise, residual_noise = generate_adversarial_low_frequency_signal(
    original_signal, clean_signal, attack_strength=1.0
)

# --- 3. 可视化设置 ---
plt.style.use('default')
plt.rcParams.update({
    'font.family': 'Times New Roman',
    'font.size': 12,  # 增加字体大小
    'axes.titlesize': 13,  # 增加标题字体大小
    'axes.labelsize': 12,  # 增加轴标签字体大小
    'xtick.labelsize': 11,  # 增加刻度标签字体大小
    'ytick.labelsize': 11,
    'legend.fontsize': 11,
    'figure.titlesize': 16,  # 增加总标题字体大小
    'lines.linewidth': 1.8,  # 增加线条粗细
    'axes.linewidth': 1.2,  # 增加轴线粗细
    'grid.linewidth': 0.8,  # 增加网格线粗细
    'grid.alpha': 0.4,  # 增加网格线透明度
    'figure.dpi': 500,  # 设置默认DPI
    'savefig.dpi': 600,  # 保存时使用更高DPI
    'savefig.bbox': 'tight',
    'savefig.pad_inches': 0.1
})

# 创建2x3的子图
fig, axs = plt.subplots(2, 3, figsize=(15, 10), dpi=400)  # 增加DPI到400
fig.suptitle('Time/Frequency Domain Analysis of DCRAD Signal Processing', 
             fontweight='bold', fontsize=16, y=0.95)

# --- 时域图 ---
signals = [original_signal, clean_signal, adversarial_signal]
titles_time = [
    '(a) Original Signal',
    '(b) Pure Signal ',
    '(c) Adversarial Signal '
]

colors = ['#1f77b4', '#2ca02c', '#ff7f0e']  # 使用更鲜明的颜色

for i in range(3):
    ax = axs[0, i]
    ax.plot(t, signals[i], lw=2.0, color=colors[i], alpha=0.9)  # 增加线条粗细和透明度
    ax.set_title(titles_time[i], fontweight='bold', fontsize=13)
    ax.set_xlabel('Time (s)', fontsize=12)
    ax.set_ylabel('Amplitude', fontsize=12)
    ax.set_xlim(0, DURATION)
    ax.set_ylim(-2.5, 2.5)
    
    # 改进网格线
    ax.grid(True, alpha=0.4, linestyle='--', linewidth=0.8)
    
    # 设置刻度线粗细
    ax.tick_params(axis='both', which='major', width=1.2, length=6)
    ax.tick_params(axis='both', which='minor', width=0.8, length=3)
    
    # 突出显示扰动区域 (只在c图中)
    if i == 2:
        # 标识恒定偏移区域（0.575-0.625秒，总长度0.05秒）
        start_time = 0.575  # 扰动开始时间
        end_time = 0.625    # 扰动结束时间
        ax.axvspan(start_time, end_time, alpha=0.3, color='red', zorder=0)

# --- 频域图 (Spectrogram) ---
titles_freq = [
    '(d) Spectrogram of Original Signal',
    '(e) Spectrogram of Clean Signal',
    '(f) Spectrogram of Low-frequency Attack Signal'
]

for i in range(3):
    ax = axs[1, i]
    
    # 计算频谱图 - 调整参数使图表大小一致
    f, t_spec, Sxx = signal.spectrogram(signals[i], SAMPLING_RATE,
                                       nperseg=128, noverlap=64)
    
    # 使用对数刻度
    Sxx_log = 10 * np.log10(Sxx + 1e-12)
    
    # 绘制频谱图 - 调整显示范围使其与时域图大小一致
    im = ax.pcolormesh(t_spec, f, Sxx_log, shading='gouraud',
                      cmap='viridis', vmin=-40, vmax=0)

    ax.set_title(titles_freq[i], fontweight='bold', fontsize=13)
    ax.set_xlabel('Time (s)', fontsize=12)
    ax.set_ylabel('Frequency (Hz)', fontsize=12)
    ax.set_xlim(0, DURATION)  # 与时域图保持相同的时间范围
    ax.set_ylim(0, 100)  # 限制频率范围突出关键区域
    
    # 设置刻度线粗细
    ax.tick_params(axis='both', which='major', width=1.2, length=6)
    ax.tick_params(axis='both', which='minor', width=0.8, length=3)
    
    # 添加颜色条 - 调整大小使其与图表协调
    cbar = fig.colorbar(im, ax=ax, shrink=0.6, aspect=20)
    cbar.set_label('Power/Frequency (dB/Hz)', fontsize=11)
    cbar.ax.tick_params(labelsize=10)
    
    # 标记关键频率 (只在第一个频谱图中添加文字标注)
    if i == 0:
        ax.text(0.05, F1_BASE+2, f'F1~{F1_BASE}Hz', color='blue', fontsize=10, fontweight='bold',
                bbox=dict(boxstyle="round,pad=0.2", facecolor="white", alpha=0.8))
        ax.text(0.05, F2_BASE+2, f'F2~{F2_BASE}Hz', color='blue', fontsize=10, fontweight='bold',
                bbox=dict(boxstyle="round,pad=0.2", facecolor="white", alpha=0.8))
    
    # 在频谱图中标记不同信号的特征
    if i == 0:
        # 原始信号：标记主要频率成分
        ax.axhline(y=F1_BASE, color='blue', linestyle='--', alpha=0.7, linewidth=2)
        ax.axhline(y=F2_BASE, color='blue', linestyle='--', alpha=0.7, linewidth=2)
    elif i == 1:
        # 干净信号：标记去噪后的频率特征
        ax.axhline(y=F1_BASE, color='green', linestyle='--', alpha=0.7, linewidth=2)
        ax.axhline(y=F2_BASE, color='green', linestyle='--', alpha=0.7, linewidth=2)
    elif i == 2:
        # 对抗性信号：标记低频攻击特征
        # 标记3Hz低频攻击频率
        ax.axhline(y=3, color='red', linestyle='-', alpha=0.8, linewidth=3)
        # 用红色区域标识低频攻击范围（0-5Hz）
        ax.axhspan(0, 5, alpha=0.2, color='red', zorder=0)
        # 标记原始信号频率成分（受到低频攻击影响）
        ax.axhline(y=F1_BASE, color='orange', linestyle='--', alpha=0.6, linewidth=2)
        ax.axhline(y=F2_BASE, color='orange', linestyle='--', alpha=0.6, linewidth=2)

# 调整布局
plt.tight_layout(rect=[0, 0, 1, 0.93])

# 保存图像 - 使用更高DPI
plt.savefig('time_frequency_analysis.png', dpi=400, bbox_inches='tight', 
            facecolor='white', edgecolor='none', pad_inches=0.1)
plt.savefig('time_frequency_analysis.pdf', dpi=400, bbox_inches='tight', 
            facecolor='white', edgecolor='none', pad_inches=0.1)

plt.show()

print("高清晰度时/频域分析图表已成功生成！")
print("实现了基于低频攻击的对抗性信号：3Hz正弦波攻击 + 包络调制")
print("文件保存为: time_frequency_analysis.png 和 time_frequency_analysis.pdf")