import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from scipy.signal import butter, filtfilt
import matplotlib.patches as patches
# from scipy.fft import fft, ifft, fftfreq  # 不再需要，已改为阶跃函数实现

# --- 1. 参数设置 - 更真实的数据 ---
SAMPLING_RATE = 500   # 采样率
DURATION = 1.0        # 时间窗口
N_SAMPLES = int(DURATION * SAMPLING_RATE)

# 基础频率参数
F1_BASE = 12  # 基础频率
F2_BASE = 18  # 次频率

# 噪声参数
NOISE_AMPLITUDE = 0.20
DRIFT_AMPLITUDE = 0.12

# 攻击参数 - 修改为低频攻击
ATTACK_START_TIME = 0.35
ATTACK_END_TIME = 0.65
ATTACK_FREQUENCY = 8  # 改为低频攻击 (8Hz)

# --- 2. 生成更真实的信号 ---
t = np.linspace(0, DURATION, N_SAMPLES, endpoint=False)

# (1) 创建真实的基础信号 - 包含频率漂移和幅度调制
# 频率漂移
freq_drift = F1_BASE + 2 * np.sin(2 * np.pi * 0.8 * t)
freq_drift2 = F2_BASE + 1.5 * np.sin(2 * np.pi * 1.2 * t)

# 幅度调制
amplitude_mod = 1.0 + 0.2 * np.sin(2 * np.pi * 3 * t)
amplitude_mod2 = 0.5 + 0.1 * np.sin(2 * np.pi * 2.5 * t)

# 基础信号
base_signal = (amplitude_mod * np.sin(2 * np.pi * freq_drift * t) + 
               amplitude_mod2 * np.sin(2 * np.pi * freq_drift2 * t))

# 添加基线漂移
baseline_drift = DRIFT_AMPLITUDE * np.sin(2 * np.pi * 0.3 * t) + 0.05 * t

# 添加谐波失真
harmonic_distortion = 0.1 * np.sin(2 * np.pi * freq_drift * 3 * t)

normal_signal_realistic = base_signal + baseline_drift + harmonic_distortion

# (2) 创建真实的噪声 - 非平稳噪声
# 高斯白噪声 - 减少噪声强度
white_noise = (NOISE_AMPLITUDE * 0.3) * np.random.randn(N_SAMPLES)

# 有色噪声 (1/f噪声) - 减少有色噪声
colored_noise = np.zeros(N_SAMPLES)
for i in range(1, N_SAMPLES//4):  # 减少频率成分
    phase = np.random.uniform(0, 2*np.pi)
    colored_noise += (1/i) * np.sin(2 * np.pi * i * t / DURATION + phase)
colored_noise *= NOISE_AMPLITUDE * 0.1  # 大幅减少有色噪声

# 脉冲噪声 - 减少脉冲数量
impulse_noise = np.zeros(N_SAMPLES)
impulse_positions = np.random.choice(N_SAMPLES, size=2, replace=False)  # 减少脉冲数量
impulse_noise[impulse_positions] = np.random.uniform(-0.2, 0.2, len(impulse_positions))  # 减少脉冲幅度

# (3) 原始信号 (a图) - 只有轻微噪声，没有攻击
original_signal = normal_signal_realistic + white_noise + colored_noise + impulse_noise

# (4) 纯净信号 (b图) - 通过滤波获得去噪后的信号
def design_clean_filter():
    # 设计带通滤波器保留正常信号频率
    low_cutoff = 8  # Hz
    high_cutoff = 30  # Hz
    nyquist = SAMPLING_RATE / 2
    low = low_cutoff / nyquist
    high = high_cutoff / nyquist
    
    b, a = butter(4, [low, high], btype='band')
    return b, a

b, a = design_clean_filter()
clean_signal = filtfilt(b, a, original_signal)

# 使用更温和的平滑，避免过度去噪
smooth_window = np.hanning(5)  # 进一步减少窗口大小，从7减少到5
smooth_window = smooth_window / np.sum(smooth_window)
clean_signal = np.convolve(clean_signal, smooth_window, 'same')

# 保留更多原始信号特征，减少去噪强度
# 将去噪后的信号与原始信号进行加权混合，保持更多原始特征
clean_signal = 0.8 * clean_signal + 0.2 * original_signal  # 增加原始信号比例

# 特别处理0.6-1.0秒区间，几乎保持原始信号不变
problem_start_idx = int(0.6 * SAMPLING_RATE)
problem_end_idx = int(1.0 * SAMPLING_RATE)
if problem_end_idx > N_SAMPLES:
    problem_end_idx = N_SAMPLES

# 在问题区间几乎保持原始信号，只做极轻微的平滑
clean_signal[problem_start_idx:problem_end_idx] = (
    0.50 * original_signal[problem_start_idx:problem_end_idx] + 
    0.50 * clean_signal[problem_start_idx:problem_end_idx]
)

# 保留合理的高频残差 - 模拟传感器微振动，但减少强度
sensor_micro_vibration = 0.02 * np.random.randn(N_SAMPLES)  # 进一步减少微振动
# 添加低频微振动成分，但减少幅度
micro_vibration_low = 0.015 * np.sin(2 * np.pi * 25 * t)  # 25Hz微振动，减少幅度
micro_vibration_high = 0.008 * np.sin(2 * np.pi * 45 * t)  # 45Hz微振动，减少幅度
clean_signal += sensor_micro_vibration + micro_vibration_low + micro_vibration_high

# (5) 基于阶跃函数的对抗性视图构建逻辑
def generate_adversarial_step_signal(original_signal, clean_signal, attack_strength=1.2):
    """
    使用阶跃函数来表示对抗性视图构建逻辑
    这更好地反映了对抗性攻击的离散性和突变性特征
    """
    # 创建基于阶跃函数的对抗性信号
    adversarial_signal = clean_signal.copy()

    # 定义多个阶跃点，模拟对抗性视图的构建过程
    step_points = [0.2, 0.4, 0.6, 0.8]  # 时间点
    step_amplitudes = [0.3, -0.5, 0.7, -0.4]  # 对应的阶跃幅度

    # 应用阶跃函数
    for i, (step_time, amplitude) in enumerate(zip(step_points, step_amplitudes)):
        step_idx = int(step_time * SAMPLING_RATE)
        if step_idx < N_SAMPLES:
            # 从阶跃点开始，信号保持在新的水平
            adversarial_signal[step_idx:] += amplitude * attack_strength

    # 添加一些渐变过渡，使阶跃更加平滑但仍保持阶跃特征
    transition_samples = int(0.01 * SAMPLING_RATE)  # 10ms的过渡时间
    for i, step_time in enumerate(step_points):
        step_idx = int(step_time * SAMPLING_RATE)
        if step_idx < N_SAMPLES - transition_samples:
            # 在阶跃点附近添加平滑过渡
            transition_end = min(step_idx + transition_samples, N_SAMPLES)
            transition_factor = np.linspace(0, 1, transition_end - step_idx)
            adversarial_signal[step_idx:transition_end] = (
                clean_signal[step_idx:transition_end] +
                step_amplitudes[i] * attack_strength * transition_factor
            )

    # 计算对抗性噪声（用于分析）
    adversarial_noise = adversarial_signal - clean_signal
    residual_noise = original_signal - clean_signal

    return adversarial_signal, adversarial_noise, residual_noise

# 生成基于阶跃函数的对抗性信号
adversarial_signal, adversarial_noise, residual_noise = generate_adversarial_step_signal(
    original_signal, clean_signal, attack_strength=1.0
)

# --- 3. 可视化设置 ---
plt.style.use('default')
plt.rcParams.update({
    'font.family': 'Times New Roman',
    'font.size': 12,  # 增加字体大小
    'axes.titlesize': 13,  # 增加标题字体大小
    'axes.labelsize': 12,  # 增加轴标签字体大小
    'xtick.labelsize': 11,  # 增加刻度标签字体大小
    'ytick.labelsize': 11,
    'legend.fontsize': 11,
    'figure.titlesize': 16,  # 增加总标题字体大小
    'lines.linewidth': 1.8,  # 增加线条粗细
    'axes.linewidth': 1.2,  # 增加轴线粗细
    'grid.linewidth': 0.8,  # 增加网格线粗细
    'grid.alpha': 0.4,  # 增加网格线透明度
    'figure.dpi': 500,  # 设置默认DPI
    'savefig.dpi': 600,  # 保存时使用更高DPI
    'savefig.bbox': 'tight',
    'savefig.pad_inches': 0.1
})

# 创建2x3的子图
fig, axs = plt.subplots(2, 3, figsize=(15, 10), dpi=400)  # 增加DPI到400
fig.suptitle('Time/Frequency Domain Analysis of DCRAD Signal Processing', 
             fontweight='bold', fontsize=16, y=0.95)

# --- 时域图 ---
signals = [original_signal, clean_signal, adversarial_signal]
titles_time = [
    '(a) Original Signal (with Mild Noise)',
    '(b) Clean Signal (Denoised)',
    '(c) Adversarial Signal (Step Function Logic)'
]

colors = ['#1f77b4', '#2ca02c', '#ff7f0e']  # 使用更鲜明的颜色

for i in range(3):
    ax = axs[0, i]
    ax.plot(t, signals[i], lw=2.0, color=colors[i], alpha=0.9)  # 增加线条粗细和透明度
    ax.set_title(titles_time[i], fontweight='bold', fontsize=13)
    ax.set_xlabel('Time (s)', fontsize=12)
    ax.set_ylabel('Amplitude', fontsize=12)
    ax.set_xlim(0, DURATION)
    ax.set_ylim(-2.5, 2.5)
    
    # 改进网格线
    ax.grid(True, alpha=0.4, linestyle='--', linewidth=0.8)
    
    # 设置刻度线粗细
    ax.tick_params(axis='both', which='major', width=1.2, length=6)
    ax.tick_params(axis='both', which='minor', width=0.8, length=3)
    
    # 突出显示扰动区域 (只在c图中)
    if i == 2:
        # 用红色半透明区域标识阶跃函数的扰动区域
        step_points = [0.2, 0.4, 0.6, 0.8]  # 与生成函数中的阶跃点对应
        for step_time in step_points:
            # 在每个阶跃点前后标识扰动区域
            start_time = max(0, step_time - 0.05)  # 扰动开始时间
            end_time = min(DURATION, step_time + 0.05)  # 扰动结束时间
            ax.axvspan(start_time, end_time, alpha=0.3, color='red', zorder=0)

# --- 频域图 (Spectrogram) ---
titles_freq = [
    '(d) Spectrogram of Original Signal',
    '(e) Spectrogram of Clean Signal',
    '(f) Spectrogram of Step-based Adversarial Signal'
]

for i in range(3):
    ax = axs[1, i]
    
    # 计算频谱图 - 使用更小的窗口突出细节
    f, t_spec, Sxx = signal.spectrogram(signals[i], SAMPLING_RATE, 
                                       nperseg=64, noverlap=32)
    
    # 使用对数刻度
    Sxx_log = 10 * np.log10(Sxx + 1e-12)
    
    # 绘制频谱图 - 使用更清晰的颜色映射
    im = ax.pcolormesh(t_spec, f, Sxx_log, shading='gouraud', 
                      cmap='viridis', vmin=-40, vmax=0)
    
    ax.set_title(titles_freq[i], fontweight='bold', fontsize=13)
    ax.set_xlabel('Time (s)', fontsize=12)
    ax.set_ylabel('Frequency (Hz)', fontsize=12)
    ax.set_ylim(0, 100)  # 限制频率范围突出关键区域
    
    # 设置刻度线粗细
    ax.tick_params(axis='both', which='major', width=1.2, length=6)
    ax.tick_params(axis='both', which='minor', width=0.8, length=3)
    
    # 添加颜色条
    cbar = fig.colorbar(im, ax=ax, shrink=0.8)
    cbar.set_label('Power/Frequency (dB/Hz)', fontsize=11)
    cbar.ax.tick_params(labelsize=10)
    
    # 标记关键频率 (只在第一个频谱图中)
    if i == 0:
        ax.axhline(y=F1_BASE, color='red', linestyle='--', alpha=0.8, linewidth=2)
        ax.axhline(y=F2_BASE, color='red', linestyle='--', alpha=0.8, linewidth=2)
        ax.text(0.05, F1_BASE+2, f'F1~{F1_BASE}Hz', color='red', fontsize=10, fontweight='bold',
                bbox=dict(boxstyle="round,pad=0.2", facecolor="white", alpha=0.8))
        ax.text(0.05, F2_BASE+2, f'F2~{F2_BASE}Hz', color='red', fontsize=10, fontweight='bold',
                bbox=dict(boxstyle="round,pad=0.2", facecolor="white", alpha=0.8))
    
    # 在c图的频谱图中标记阶跃函数特征
    if i == 2:
        # 用红色线条标记阶跃转换点对应的频率特征，不添加文字
        ax.axhline(y=5, color='red', linestyle='--', alpha=0.7, linewidth=2)
        # 用红色区域标识低频扰动范围
        ax.axhspan(0, 10, alpha=0.2, color='red', zorder=0)

# 调整布局
plt.tight_layout(rect=[0, 0, 1, 0.93])

# 保存图像 - 使用更高DPI
plt.savefig('time_frequency_analysis.png', dpi=400, bbox_inches='tight', 
            facecolor='white', edgecolor='none', pad_inches=0.1)
plt.savefig('time_frequency_analysis.pdf', dpi=400, bbox_inches='tight', 
            facecolor='white', edgecolor='none', pad_inches=0.1)

plt.show()

print("高清晰度时/频域分析图表已成功生成！")
print("实现了基于阶跃函数的对抗性视图构建逻辑")
print("文件保存为: time_frequency_analysis.png 和 time_frequency_analysis.pdf")